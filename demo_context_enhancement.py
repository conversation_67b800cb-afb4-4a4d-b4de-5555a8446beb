#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示上下文增强功能的完整流程
"""

def demo_context_enhancement():
    """演示上下文增强功能"""
    
    print("=== AI逻辑上下文增强功能演示 ===\n")
    
    print("功能说明：")
    print("1. 当处理子list时，如果前一组的最后一项是explanation")
    print("2. 就把前一组的最后3行作为上下文，一起交给AI")
    print("3. 在处理AI返回结果时，过滤掉上下文部分（因为已经是对的了）")
    print()
    
    # 模拟数据
    explanation_block = [
        # 第一个子块 - 前一组
        [
            "100:answer@这是第一组的答案1",
            "101:answer@这是第一组的答案2", 
            "102:explanation@这是第一组的解释（前一组最后一项是explanation）"
        ],
        # 第二个子块 - 当前要处理的组
        [
            "103:answer@这是第二组的答案1",
            "104:answer@这是第二组的答案2",
            "105:explanation@这是第二组的解释"
        ]
    ]
    
    print("=== 模拟数据 ===")
    for i, sub_block in enumerate(explanation_block):
        print(f"子块 {i}:")
        for item in sub_block:
            print(f"  {item}")
    print()
    
    # 模拟处理第二个子块（索引为1）
    current_sub_block_idx = 1
    current_sub_block = explanation_block[current_sub_block_idx]
    
    print(f"=== 处理子块 {current_sub_block_idx} ===")
    
    # 1. 检查是否需要添加上下文
    def get_context_from_previous_block(explanation_block, current_sub_block_idx):
        if current_sub_block_idx <= 0:
            return None
            
        previous_sub_block = explanation_block[current_sub_block_idx - 1]
        
        if not previous_sub_block:
            return None
            
        last_item = previous_sub_block[-1]
        parts = last_item.split(':', 1)
        if len(parts) < 2:
            return None
            
        label_content = parts[1]
        label_parts = label_content.split('@', 1)
        if len(label_parts) < 2:
            return None
            
        data_label = label_parts[0]
        
        # 如果最后一项是explanation，获取最后3行作为上下文
        if data_label == 'explanation':
            context_lines = previous_sub_block[-3:] if len(previous_sub_block) >= 3 else previous_sub_block
            return context_lines
            
        return None
    
    context_lines = get_context_from_previous_block(explanation_block, current_sub_block_idx)
    
    if context_lines:
        print("✓ 检测到前一组最后一项是explanation，获取上下文")
        print("上下文内容:")
        for line in context_lines:
            print(f"  {line}")
    else:
        print("✗ 前一组最后一项不是explanation，无需上下文")
    print()
    
    # 2. 准备AI输入数据
    def prepare_ai_input_with_context(sub_block, context_lines=None):
        input_lines = []
        context_line_numbers = []

        # 如果有上下文，先添加上下文数据
        if context_lines:
            input_lines.append("=== 上下文信息（仅供参考，不需要判断） ===")
            for item in context_lines:
                parts = item.split(':', 1)
                if len(parts) >= 2:
                    index = parts[0]
                    label_content = parts[1]
                    label_parts = label_content.split('@', 1)
                    if len(label_parts) >= 2:
                        content = label_parts[1]
                        # 为上下文行号添加特殊前缀
                        context_line_id = f"ctx_{index}"
                        input_lines.append(f"{context_line_id}:{content}")
                        context_line_numbers.append(context_line_id)
            
            input_lines.append("=== 需要判断的内容 ===")

        # 添加当前子块的数据
        for item in sub_block:
            parts = item.split(':', 1)
            if len(parts) >= 2:
                index = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    content = label_parts[1]
                    input_lines.append(f"{index}:{content}")

        result = '\n'.join(input_lines)
        return result, context_line_numbers
    
    ai_input, context_line_numbers = prepare_ai_input_with_context(current_sub_block, context_lines)
    
    print("=== AI输入数据 ===")
    print(ai_input)
    print()
    print(f"上下文行号: {context_line_numbers}")
    print()
    
    # 3. 模拟AI返回结果
    mock_ai_result = ["ctx_100", "ctx_102", "103", "105"]  # 包含上下文和当前数据的行号
    print("=== 模拟AI返回结果 ===")
    print(f"原始AI结果: {mock_ai_result}")
    print("说明: AI可能会错误地标记上下文中的内容，我们需要过滤掉这些")
    print()
    
    # 4. 过滤上下文相关的结果
    def filter_context_from_ai_result(ai_result, context_line_numbers):
        if not context_line_numbers:
            return ai_result
            
        filtered_result = [line_num for line_num in ai_result if line_num not in context_line_numbers]
        return filtered_result
    
    filtered_result = filter_context_from_ai_result(mock_ai_result, context_line_numbers)
    
    print("=== 过滤后的AI结果 ===")
    print(f"过滤前: {mock_ai_result}")
    print(f"过滤后: {filtered_result}")
    print(f"过滤掉的上下文行号: {[x for x in mock_ai_result if x in context_line_numbers]}")
    print()
    
    print("=== 总结 ===")
    print("✓ 成功检测到前一组的最后一项是explanation")
    print("✓ 成功获取前一组的最后3行作为上下文")
    print("✓ 成功将上下文添加到AI输入中，并用特殊标记区分")
    print("✓ 成功过滤掉AI结果中的上下文相关内容")
    print("✓ 只保留当前数据的AI判断结果，确保不会错误修改已经正确的内容")

if __name__ == "__main__":
    demo_context_enhancement()
