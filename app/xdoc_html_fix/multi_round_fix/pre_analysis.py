# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/12 15:24
import re
import json
import asyncio
from typing import List, Dict, Any, Tuple

from app.basic.util import html_util
from app.enums.prompt import GetPrompt
from app.basic.api.doubao import <PERSON><PERSON><PERSON>, DBModel
from app.basic.log import logger


class PreAnalysis:
    """
    AI模型分析处理模块
    负责对简化后的HTML数据进行AI判断和处理
    """

    def __init__(self, subject: str, task_id: str):
        self.subject = subject
        self.task_id = task_id

    async def analyze_simplified_html_list(self, simplified_html_list: List[str], html_elements: List[str]) -> str:
        """
        分析简化后的HTML列表，执行AI判断并应用结果
        
        Args:
            simplified_html_list: 简化的HTML列表，格式为 "index:data-label@content"
            html_elements: 原始HTML元素列表
            
        Returns:
            str: 更新后的HTML数据
        """
        # 处理简化后的数据列表
        processed_result = self.process_simplified_list(simplified_html_list)

        # 执行AI判断explanation功能
        if processed_result:
            try:
                ai_judgment_result = await self.ai_judge_explanation(processed_result)
                logger.info(f"AI判断完成，识别出需要重新标记的内容")

                # 应用AI判断结果到HTML数据
                self._apply_ai_judgment_to_html(ai_judgment_result, simplified_html_list, html_elements)

            except Exception as e:
                logger.error(f"AI判断失败: {e}")
                # AI判断失败不影响主流程，继续执行

        # 重新构建HTML数据
        return html_util.join_html(html_elements)

    def process_simplified_list(self, simplified_html_list):
        """
        处理简化后的HTML列表，按照要求进行分组和拆分

        修改后的逻辑：
        1. 不按照 header 分组，直接在整个列表中找所有连续的 explanation
        2. 找到连续的 explanation 后，全部保留（不只取后面三行）
        3. 每组连续的 explanation + 后面的所有非 explanation 内容作为一个 processed_result 的项
        4. 分割策略：按照固定10个元素进行分割（从8个增加到10个）
        5. 如果最后一项是 explanation，将其合并到前一个组中

        Args:
            simplified_html_list: 格式为 "index:data-label@content" 的列表

        Returns:
            list: 处理后的结果，格式为 [{index: 0, list: [[...], [...]]}, ...]
                  每个连续explanation组作为一个独立的结果项
        """
        # 1. 直接在整个列表中找到所有连续的explanation组
        consecutive_explanation_groups = self._find_consecutive_explanation_groups(simplified_html_list)

        # 3. 处理每个连续explanation组
        result = []
        result_index = 0

        for explanation_group in consecutive_explanation_groups:
            if explanation_group:
                # 全部保留explanation（不过滤）
                all_explanations = explanation_group

                # 按照原始索引顺序构建这个explanation组的完整内容
                all_content = self._build_ordered_content_for_explanation_group(simplified_html_list, all_explanations)

                # 按照固定10个元素进行分割，最后一个块不足5个时合并到前一个块
                result_lists = []
                for i in range(0, len(all_content), 10):
                    chunk = all_content[i:i+10]
                    result_lists.append(chunk)

                # 如果最后一个块数量不足5，合并到前一个块
                if len(result_lists) > 1 and len(result_lists[-1]) < 5:
                    last_chunk = result_lists.pop()  # 移除最后一个块
                    result_lists[-1].extend(last_chunk)  # 合并到前一个块

                result.append({
                    'index': result_index,
                    'list': result_lists
                })
                result_index += 1

        return result

    def _find_consecutive_explanation_groups(self, simplified_html_list):
        """
        找到所有连续的 explanation 组

        Args:
            simplified_html_list: 完整的简化HTML列表

        Returns:
            list: 连续explanation组的列表，每个组包含连续的explanation项目及其在列表中的索引
        """
        consecutive_groups = []
        current_group = []

        for i, item in enumerate(simplified_html_list):
            # 解析当前项
            parts = item.split(':', 1)
            if len(parts) < 2:
                if current_group:
                    consecutive_groups.append(current_group)
                    current_group = []
                continue

            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                if current_group:
                    consecutive_groups.append(current_group)
                    current_group = []
                continue

            data_label = label_parts[0]

            # 如果是 explanation，加入当前组
            if data_label == 'explanation':
                current_group.append({
                    'item': item,
                    'index': i
                })
            else:
                # 如果不是 explanation，结束当前组
                if current_group:
                    consecutive_groups.append(current_group)
                    current_group = []

        # 处理最后一个组
        if current_group:
            # 检查是否需要合并：
            # 1. 当前组只有一个explanation
            # 2. 这个explanation是列表的最后一项
            # 3. 前面已经有其他组存在
            # 4. 这个explanation后面没有其他内容
            should_merge = (
                len(current_group) == 1 and  # 只有一个explanation
                current_group[0]['index'] == len(simplified_html_list) - 1 and  # 是列表最后一项
                len(consecutive_groups) > 0  # 前面有其他组
            )

            if should_merge:
                # 将最后一个explanation合并到前一个组中
                consecutive_groups[-1].extend(current_group)
            else:
                # 否则正常添加
                consecutive_groups.append(current_group)

        return consecutive_groups

    def _build_ordered_content_for_explanation_group(self, simplified_html_list, explanation_group):
        """
        按照原始索引顺序构建explanation组的完整内容

        Args:
            simplified_html_list: 完整的简化HTML列表
            explanation_group: explanation组，包含多个explanation项目

        Returns:
            list: 按原始索引顺序排列的完整内容列表
        """
        if not explanation_group:
            return []

        # 获取第一个和最后一个explanation的索引
        first_explanation_index = explanation_group[0]['index']
        last_explanation_index = explanation_group[-1]['index']



        # 收集从第一个explanation开始到最后一个explanation结束的所有内容
        all_content = []

        # 从第一个explanation开始收集
        for i in range(first_explanation_index, last_explanation_index + 1):
            all_content.append(simplified_html_list[i])

        # 收集最后一个explanation后面的内容，直到遇到下一个explanation或列表结束
        j = last_explanation_index + 1
        while j < len(simplified_html_list):
            next_item = simplified_html_list[j]
            next_parts = next_item.split(':', 1)
            if len(next_parts) >= 2:
                next_label_content = next_parts[1]
                next_label_parts = next_label_content.split('@', 1)
                if len(next_label_parts) >= 2:
                    next_data_label = next_label_parts[0]
                    # 如果遇到explanation，立即停止收集
                    if next_data_label == 'explanation':
                        break

            # 收集所有非explanation内容
            all_content.append(next_item)
            j += 1

        return all_content

    def _collect_content_for_explanation_group(self, simplified_html_list, explanation_group):
        """
        收集explanation组覆盖范围内的所有非explanation内容

        Args:
            simplified_html_list: 完整的简化HTML列表
            explanation_group: explanation组，包含多个explanation项目

        Returns:
            list: 收集到的所有非explanation内容列表
        """
        if not explanation_group:
            return []

        # 获取第一个和最后一个explanation的索引
        first_explanation_index = explanation_group[0]['index']
        last_explanation_index = explanation_group[-1]['index']

        # 获取所有explanation的索引集合
        explanation_indices = {item['index'] for item in explanation_group}

        collected_items = []

        # 从第一个explanation后开始，到最后一个explanation后结束
        start_index = first_explanation_index + 1

        # 收集第一个explanation到最后一个explanation之间的所有非explanation内容
        for i in range(start_index, last_explanation_index):
            if i not in explanation_indices:
                collected_items.append(simplified_html_list[i])

        # 收集最后一个explanation后面的内容，直到遇到下一个explanation或列表结束
        j = last_explanation_index + 1
        while j < len(simplified_html_list):
            next_item = simplified_html_list[j]
            next_parts = next_item.split(':', 1)
            if len(next_parts) >= 2:
                next_label_content = next_parts[1]
                next_label_parts = next_label_content.split('@', 1)
                if len(next_label_parts) >= 2:
                    next_data_label = next_label_parts[0]
                    # 如果遇到explanation，立即停止收集
                    if next_data_label == 'explanation':
                        break

            # 收集所有非explanation内容
            collected_items.append(next_item)
            j += 1

        return collected_items

    def _collect_content_until_next_explanation(self, simplified_html_list, start_index):
        """
        从指定索引后开始收集所有非explanation内容，直到遇到下一个explanation

        Args:
            simplified_html_list: 完整的简化HTML列表
            start_index: 开始收集的索引位置

        Returns:
            list: 收集到的所有非explanation内容列表
        """
        collected_items = []
        j = start_index + 1

        while j < len(simplified_html_list):
            next_item = simplified_html_list[j]
            next_parts = next_item.split(':', 1)
            if len(next_parts) < 2:
                collected_items.append(next_item)
                j += 1
                continue

            next_label_content = next_parts[1]
            next_label_parts = next_label_content.split('@', 1)
            if len(next_label_parts) < 2:
                collected_items.append(next_item)
                j += 1
                continue

            next_data_label = next_label_parts[0]

            # 如果遇到explanation，立即停止收集
            if next_data_label == 'explanation':
                break

            # 收集所有非explanation内容（包括有其他标签的内容）
            collected_items.append(next_item)
            j += 1

        return collected_items

    async def ai_judge_explanation(self, processed_result: List[Dict[str, Any]], model: str = DBModel.DS_V3.value) -> Dict[str, Any]:
        """
        使用AI判断哪些内容应该被标记为explanation

        Args:
            processed_result: process_simplified_list的处理结果
            model: 使用的AI模型

        Returns:
            Dict: 包含原始数据和AI判断结果的字典
        """
        logger.info(f"开始AI判断explanation，共{len(processed_result)}个分组")

        ai_results = []

        for group_idx, group in enumerate(processed_result):
            logger.info(f"处理分组 {group_idx}，共{len(group['list'])}个explanation块")

            group_results = []
            explanation_blocks = self._group_explanation_blocks(group['list'])

            for block_idx, explanation_block in enumerate(explanation_blocks):
                logger.info(f"处理explanation块 {block_idx}，共{len(explanation_block)}个子块")

                block_result = await self._process_explanation_block(explanation_block, model)
                if block_result:
                    group_results.append({
                        'block_index': block_idx,
                        'results': block_result
                    })

            if group_results:
                ai_results.append({
                    'group_index': group_idx,
                    'blocks': group_results
                })

        return {
            'original_processed_result': processed_result,
            'ai_judgment_results': ai_results
        }

    def _group_explanation_blocks(self, explanation_list: List[List[str]]) -> List[List[List[str]]]:
        """
        将explanation块按照原始explanation进行分组

        Args:
            explanation_list: 所有的explanation块列表

        Returns:
            List: 按原始explanation分组的块列表
        """
        grouped_blocks = []
        current_group = []
        current_explanation = None

        for block in explanation_list:
            # 找到当前块中的explanation
            block_explanation = None
            for item in block:
                parts = item.split(':', 1)
                if len(parts) >= 2:
                    label_content = parts[1]
                    label_parts = label_content.split('@', 1)
                    if len(label_parts) >= 2 and label_parts[0] == 'explanation':
                        block_explanation = item
                        break

            if block_explanation:
                # 如果是新的explanation，开始新的分组
                if current_explanation != block_explanation:
                    if current_group:
                        grouped_blocks.append(current_group)
                    current_group = [block]
                    current_explanation = block_explanation
                else:
                    # 同一个explanation的后续块
                    current_group.append(block)

        # 添加最后一个分组
        if current_group:
            grouped_blocks.append(current_group)

        return grouped_blocks

    async def _process_explanation_block(self, explanation_block: List[List[str]], model: str) -> List[Dict[str, Any]]:
        """
        处理单个explanation块的所有子块

        Args:
            explanation_block: 同一个explanation的所有子块
            model: AI模型

        Returns:
            List: AI判断结果列表
        """
        results = []

        for sub_block_idx, sub_block in enumerate(explanation_block):
            logger.info(f"处理子块 {sub_block_idx}")

            # 检查是否需要添加上下文
            context_lines = None
            if sub_block_idx > 0:
                context_lines = self._get_context_from_previous_block(explanation_block, sub_block_idx)

            # 准备AI判断的输入数据
            input_data, context_line_numbers = self._prepare_ai_input(sub_block, context_lines)

            try:
                # 调用AI进行判断
                ai_result = await self._call_ai_judgment(input_data, model)

                if ai_result:
                    # 过滤掉上下文相关的AI结果
                    filtered_ai_result = self._filter_context_from_ai_result(ai_result, context_line_numbers)

                    results.append({
                        'sub_block_index': sub_block_idx,
                        'input_data': input_data,
                        'ai_result': filtered_ai_result,
                        'original_block': sub_block,
                        'context_lines': context_lines,
                        'context_line_numbers': context_line_numbers
                    })

                # 检查连续性：如果当前子块的最后一个项目不是explanation，停止处理后续子块
                if not self._should_continue_processing(ai_result, sub_block):
                    logger.info(f"子块 {sub_block_idx} 的最后项目不是explanation，停止处理后续子块")
                    break

            except Exception as e:
                logger.error(f"AI判断失败: {e}")
                # 发生错误时也停止处理后续子块
                break

        return results

    def _get_context_from_previous_block(self, explanation_block: List[List[str]], current_sub_block_idx: int) -> List[str]:
        """
        获取前一组的最后3行作为上下文

        Args:
            explanation_block: 当前explanation块的所有子块
            current_sub_block_idx: 当前子块的索引

        Returns:
            List[str]: 前一组的最后3行，如果前一组的最后一项是explanation的话
        """
        if current_sub_block_idx <= 0:
            return None

        previous_sub_block = explanation_block[current_sub_block_idx - 1]

        # 检查前一组的最后一项是否是explanation
        if not previous_sub_block:
            return None

        last_item = previous_sub_block[-1]
        parts = last_item.split(':', 1)
        if len(parts) < 2:
            return None

        label_content = parts[1]
        label_parts = label_content.split('@', 1)
        if len(label_parts) < 2:
            return None

        data_label = label_parts[0]

        # 如果最后一项是explanation，获取最后3行作为上下文
        if data_label == 'explanation':
            # 获取最后3行，如果不足3行就全部获取
            context_lines = previous_sub_block[-3:] if len(previous_sub_block) >= 3 else previous_sub_block
            logger.info(f"从前一组获取上下文，共{len(context_lines)}行")
            return context_lines

        return None

    def _filter_context_from_ai_result(self, ai_result: List[str], context_line_numbers: List[str]) -> List[str]:
        """
        从AI结果中过滤掉上下文相关的行号

        Args:
            ai_result: AI返回的行号列表
            context_line_numbers: 上下文的行号列表

        Returns:
            List[str]: 过滤后的AI结果
        """
        if not context_line_numbers:
            return ai_result

        # 过滤掉上下文相关的行号
        filtered_result = [line_num for line_num in ai_result if line_num not in context_line_numbers]

        if len(filtered_result) != len(ai_result):
            logger.info(f"过滤上下文：原始AI结果{len(ai_result)}个，过滤后{len(filtered_result)}个")

        return filtered_result

    def _prepare_ai_input(self, sub_block: List[str], context_lines: List[str] = None) -> Tuple[str, List[str]]:
        """
        准备AI判断的输入数据，去掉所有label前缀，只保留行号和内容
        如果有上下文，将上下文添加到输入数据前面

        Args:
            sub_block: 子块数据
            context_lines: 上下文数据（前一组的最后3行）

        Returns:
            tuple: (格式化的输入数据, 上下文行号列表)
        """
        input_lines = []
        context_line_numbers = []

        # 如果有上下文，先添加上下文数据
        if context_lines:
            input_lines.append("=== 上下文信息（仅供参考，不需要判断） ===")
            for item in context_lines:
                parts = item.split(':', 1)
                if len(parts) >= 2:
                    index = parts[0]
                    label_content = parts[1]
                    label_parts = label_content.split('@', 1)
                    if len(label_parts) >= 2:
                        content = label_parts[1]
                        # 为上下文行号添加特殊前缀
                        context_line_id = f"ctx_{index}"
                        input_lines.append(f"{context_line_id}:{content}")
                        context_line_numbers.append(context_line_id)

            input_lines.append("=== 需要判断的内容 ===")

        # 添加当前子块的数据
        for item in sub_block:
            parts = item.split(':', 1)
            if len(parts) >= 2:
                index = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    content = label_parts[1]
                    # 只保留行号和内容，去掉所有label前缀
                    input_lines.append(f"{index}:{content}")

        result = '\n'.join(input_lines)

        # 调试断点：检查是否包含关键词
        debugKeyWords = "本题考查学生筛选并概括文中信息的能力"
        if debugKeyWords in result:
            # 在这里可以设置断点进行调试
            logger.info(f"发现关键词 {debugKeyWords}，AI输入数据: {result}")

        if context_lines:
            logger.info(f"准备AI输入数据，包含{len(context_lines)}行上下文，{len(sub_block)}行待判断内容")

        return result, context_line_numbers

    async def _call_ai_judgment(self, input_data: str, model: str) -> List[str]:
        """
        调用AI进行判断

        Args:
            input_data: 输入数据
            model: AI模型

        Returns:
            List[str]: AI判断结果的ID列表
        """
        try:
            # 获取prompt
            prompt = GetPrompt.analysis_fix(input_data)

            # 调用AI
            response, tokens = await Doubao.async_chat(prompt, model, temperature=0.1)

            logger.info(f"AI调用成功，使用tokens: {tokens}")

            # 解析JSON响应
            try:
                # 清理响应中的markdown标记
                cleaned_response = self._clean_ai_response(response)
                result = json.loads(cleaned_response)
                if isinstance(result, list):
                    return result
                else:
                    logger.warning(f"AI返回格式不正确: {response}")
                    return []
            except json.JSONDecodeError as e:
                logger.error(f"AI返回JSON解析失败: {e}, response: {response}")
                return []

        except Exception as e:
            logger.error(f"AI调用失败: {e}")
            raise

    def _clean_ai_response(self, response: str) -> str:
        """
        清理AI响应中的markdown标记和其他格式化字符

        Args:
            response: 原始AI响应

        Returns:
            str: 清理后的JSON字符串
        """
        # 去除首尾空白
        cleaned = response.strip()

        # 移除可能的markdown代码块标记
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]  # 移除 '```json'
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:]   # 移除 '```'

        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]  # 移除结尾的 '```'

        # 再次去除空白
        cleaned = cleaned.strip()

        # 移除可能的其他格式化标记
        lines = cleaned.split('\n')
        json_lines = []
        in_json = False

        for line in lines:
            line = line.strip()
            # 跳过空行和注释行
            if not line or line.startswith('#') or line.startswith('//'):
                continue

            # 检查是否是JSON开始
            if line.startswith('[') or line.startswith('{'):
                in_json = True

            if in_json:
                json_lines.append(line)

            # 检查是否是JSON结束
            if line.endswith(']') or line.endswith('}'):
                break

        # 如果找到了JSON内容，返回拼接的结果
        if json_lines:
            result = '\n'.join(json_lines)
            logger.debug(f"清理后的AI响应: {result}")
            return result

        # 如果没有找到明确的JSON结构，返回原始清理结果
        logger.debug(f"未找到明确JSON结构，返回清理后的响应: {cleaned}")
        return cleaned

    def _should_continue_processing(self, ai_result: List[str], sub_block: List[str]) -> bool:
        """
        判断是否应该继续处理下一个子块

        Args:
            ai_result: AI判断结果
            sub_block: 当前子块

        Returns:
            bool: 是否继续处理
        """
        if not ai_result:
            return False

        # 获取子块中最后一个非explanation项目的ID
        last_non_explanation_id = None
        for item in reversed(sub_block):
            parts = item.split(':', 1)
            if len(parts) >= 2:
                index = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    data_label = label_parts[0]
                    if data_label != 'explanation':
                        last_non_explanation_id = index
                        break

        # 如果最后一个非explanation项目在AI结果中，说明应该继续处理
        return last_non_explanation_id in ai_result if last_non_explanation_id else False

    def _apply_ai_judgment_to_html(self, ai_judgment_result: Dict[str, Any], simplified_html_list: List[str], html_elements: List[str]):
        """
        将AI判断结果应用到HTML数据中
        根据AI返回的行号列表，对HTML数据进行处理（添加或移除explanation标签）

        Args:
            ai_judgment_result: AI判断结果
            simplified_html_list: 简化的HTML列表
            html_elements: 原始HTML元素列表
        """
        try:
            # 1. 提取所有AI识别为explanation的行号
            ai_explanation_line_numbers = self._extract_line_numbers_from_ai_result(ai_judgment_result)

            if not ai_explanation_line_numbers:
                logger.info("AI判断结果中没有识别出explanation内容")
                return

            logger.info(f"AI识别出 {len(ai_explanation_line_numbers)} 个行号为explanation: {ai_explanation_line_numbers}")

            # 2. 获取当前已标记为explanation的行号
            current_explanation_line_numbers = self._get_current_explanation_line_numbers(simplified_html_list)
            logger.info(f"当前已标记为explanation的行号: {current_explanation_line_numbers}")

            # 3. 计算需要添加和移除explanation标签的行号
            lines_to_add = set(ai_explanation_line_numbers) - set(current_explanation_line_numbers)
            lines_to_remove = set(current_explanation_line_numbers) - set(ai_explanation_line_numbers)

            logger.info(f"需要添加explanation标签的行号: {lines_to_add}")
            logger.info(f"需要移除explanation标签的行号: {lines_to_remove}")

            # 4. 更新HTML元素
            updated_count = 0

            # 添加explanation标签
            for line_number in lines_to_add:
                if self._update_html_element_explanation(html_elements, line_number, add=True):
                    updated_count += 1

            # 移除explanation标签
            for line_number in lines_to_remove:
                if self._update_html_element_explanation(html_elements, line_number, add=False):
                    updated_count += 1

            logger.info(f"成功更新了 {updated_count} 个HTML元素的data-label属性")

        except Exception as e:
            logger.error(f"应用AI判断结果失败: {e}")
            # 不抛出异常，避免影响主流程

    def _extract_line_numbers_from_ai_result(self, ai_judgment_result: Dict[str, Any]) -> List[str]:
        """
        从AI判断结果中提取所有被识别为explanation的行号

        Args:
            ai_judgment_result: AI判断结果

        Returns:
            List[str]: 被识别为explanation的行号列表
        """
        line_numbers = []

        ai_results = ai_judgment_result.get('ai_judgment_results', [])
        for group_result in ai_results:
            for block_result in group_result.get('blocks', []):
                for sub_result in block_result.get('results', []):
                    ai_result = sub_result.get('ai_result', [])
                    if isinstance(ai_result, list):
                        line_numbers.extend(ai_result)

        # 去重并保持顺序
        unique_line_numbers = []
        seen = set()
        for line_number in line_numbers:
            if line_number not in seen:
                unique_line_numbers.append(line_number)
                seen.add(line_number)

        return unique_line_numbers

    def _get_current_explanation_line_numbers(self, simplified_html_list: List[str]) -> List[str]:
        """
        获取当前已标记为explanation的行号

        Args:
            simplified_html_list: 简化的HTML列表

        Returns:
            List[str]: 当前已标记为explanation的行号列表
        """
        explanation_line_numbers = []

        for item in simplified_html_list:
            parts = item.split(':', 1)
            if len(parts) >= 2:
                line_number = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    data_label = label_parts[0]
                    if data_label == 'explanation':
                        explanation_line_numbers.append(line_number)

        return explanation_line_numbers

    def _update_html_element_explanation(self, html_elements: List[str], line_number: str, add: bool) -> bool:
        """
        更新指定行号的HTML元素的explanation标签

        Args:
            html_elements: HTML元素列表
            line_number: 行号
            add: True表示添加explanation标签，False表示移除

        Returns:
            bool: 是否成功更新
        """
        try:
            element_index = int(line_number)
            if 0 <= element_index < len(html_elements):
                original_element = html_elements[element_index]

                if add:
                    updated_element = self._add_or_update_explanation_label(original_element)
                else:
                    updated_element = self._remove_explanation_label(original_element)

                if updated_element != original_element:
                    html_elements[element_index] = updated_element
                    logger.debug(f"{'添加' if add else '移除'}元素 {element_index} 的explanation标签")
                    return True
            else:
                logger.warning(f"元素索引 {element_index} 超出范围")
        except ValueError:
            logger.warning(f"无法解析行号: {line_number}")

        return False

    def _remove_explanation_label(self, html_element: str) -> str:
        """
        移除HTML元素的data-label="explanation"属性

        Args:
            html_element: 原始HTML元素字符串

        Returns:
            str: 移除explanation标签后的HTML元素字符串
        """
        # 检查是否是p标签
        if not html_element.strip().startswith('<p'):
            return html_element

        # 使用正则表达式处理p标签
        p_tag_pattern = re.compile(r'<p([^>]*)>')
        match = p_tag_pattern.search(html_element)

        if not match:
            return html_element

        attributes = match.group(1)

        # 移除data-label="explanation"属性
        data_label_pattern = re.compile(r'\s*data-label="explanation"')
        new_attributes = data_label_pattern.sub('', attributes)

        # 清理多余的空格
        new_attributes = re.sub(r'\s+', ' ', new_attributes).strip()

        # 构建新的p标签
        if new_attributes:
            new_p_tag = f'<p {new_attributes}>'
        else:
            new_p_tag = '<p>'

        # 替换原始HTML中的p标签
        updated_element = p_tag_pattern.sub(new_p_tag, html_element)

        return updated_element

    def _add_or_update_explanation_label(self, html_element: str) -> str:
        """
        为HTML元素添加或更新data-label="explanation"属性

        Args:
            html_element: 原始HTML元素字符串

        Returns:
            str: 更新后的HTML元素字符串
        """
        # 检查是否是p标签
        if not html_element.strip().startswith('<p'):
            return html_element

        # 使用正则表达式处理p标签
        p_tag_pattern = re.compile(r'<p([^>]*)>')
        match = p_tag_pattern.search(html_element)

        if not match:
            return html_element

        attributes = match.group(1)

        # 检查是否已有data-label属性
        data_label_pattern = re.compile(r'\s*data-label="[^"]*"')

        if data_label_pattern.search(attributes):
            # 替换现有的data-label属性
            new_attributes = data_label_pattern.sub(' data-label="explanation"', attributes)
        else:
            # 添加新的data-label属性
            new_attributes = attributes + ' data-label="explanation"'

        # 清理多余的空格
        new_attributes = re.sub(r'\s+', ' ', new_attributes).strip()

        # 构建新的p标签
        if new_attributes:
            new_p_tag = f'<p {new_attributes}>'
        else:
            new_p_tag = '<p data-label="explanation">'

        # 替换原始HTML中的p标签
        updated_element = p_tag_pattern.sub(new_p_tag, html_element)

        return updated_element
