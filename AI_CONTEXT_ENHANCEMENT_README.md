# AI逻辑上下文增强功能

## 功能概述

在原有的AI逻辑基础上，新增了上下文增强功能。当处理子list时，如果前一组的最后一项是explanation，就把前一组的最后3行作为上下文，一起交给AI进行判断。在处理AI返回结果时，会自动过滤掉上下文部分的结果，确保不会错误修改已经正确的内容。

## 主要修改

### 1. `_process_explanation_block` 方法增强

**位置**: `app/xdoc_html_fix/multi_round_fix/pre_analysis.py:411-447`

**修改内容**:
- 添加上下文检测逻辑
- 调用新的上下文获取方法
- 传递上下文信息给AI输入准备方法
- 过滤AI结果中的上下文相关内容

**关键代码**:
```python
# 检查是否需要添加上下文
context_lines = None
if sub_block_idx > 0:
    context_lines = self._get_context_from_previous_block(explanation_block, sub_block_idx)

# 准备AI判断的输入数据
input_data, context_line_numbers = self._prepare_ai_input(sub_block, context_lines)

# 过滤掉上下文相关的AI结果
filtered_ai_result = self._filter_context_from_ai_result(ai_result, context_line_numbers)
```

### 2. 新增 `_get_context_from_previous_block` 方法

**位置**: `app/xdoc_html_fix/multi_round_fix/pre_analysis.py:451-481`

**功能**: 
- 检查前一组的最后一项是否是explanation
- 如果是，获取前一组的最后3行作为上下文
- 如果前一组不足3行，则获取全部内容

**逻辑流程**:
1. 检查当前子块索引是否大于0
2. 获取前一个子块
3. 检查前一个子块的最后一项是否是explanation
4. 如果是explanation，返回最后3行作为上下文

### 3. 新增 `_filter_context_from_ai_result` 方法

**位置**: `app/xdoc_html_fix/multi_round_fix/pre_analysis.py:483-512`

**功能**:
- 从AI返回结果中过滤掉上下文相关的行号
- 确保只处理当前数据的AI判断结果
- 避免错误修改已经正确的上下文内容

### 4. 增强 `_prepare_ai_input` 方法

**位置**: `app/xdoc_html_fix/multi_round_fix/pre_analysis.py:514-571`

**主要改进**:
- 添加可选的`context_lines`参数
- 支持在AI输入中包含上下文信息
- 为上下文行号添加特殊前缀`ctx_`
- 返回元组：`(AI输入数据, 上下文行号列表)`

**AI输入格式**:
```
=== 上下文信息（仅供参考，不需要判断） ===
ctx_100:上下文内容1
ctx_101:上下文内容2
ctx_102:上下文内容3
=== 需要判断的内容 ===
103:当前内容1
104:当前内容2
105:当前内容3
```

## 工作流程

### 1. 上下文检测
```
当前子块索引 > 0 
    ↓
获取前一个子块
    ↓
检查最后一项是否为explanation
    ↓
是 → 获取最后3行作为上下文
否 → 无需上下文
```

### 2. AI输入准备
```
有上下文？
    ↓
是 → 添加上下文标记 + 上下文内容 + 分隔符 + 当前内容
否 → 只添加当前内容
    ↓
返回 (AI输入数据, 上下文行号列表)
```

### 3. AI结果处理
```
AI返回结果
    ↓
过滤上下文行号
    ↓
只保留当前数据的判断结果
    ↓
应用到HTML数据
```

## 测试验证

### 测试文件
- `test_context_logic.py`: 基础逻辑测试
- `test_full_context_logic.py`: 完整流程测试
- `test_real_html_context.py`: 真实HTML数据测试
- `demo_context_enhancement.py`: 功能演示

### 测试结果
✅ 上下文检测逻辑正确
✅ AI输入格式正确
✅ 上下文过滤逻辑正确
✅ 与真实HTML数据兼容

## 使用示例

使用提供的HTML文件进行测试：
```bash
python3 demo_context_enhancement.py
```

## 注意事项

1. **上下文标识**: 上下文行号使用`ctx_`前缀，避免与正常行号冲突
2. **过滤机制**: AI结果中包含`ctx_`前缀的行号会被自动过滤
3. **向后兼容**: 如果没有上下文，功能行为与原来完全一致
4. **错误处理**: 上下文处理失败不会影响主流程

## 效果

通过添加上下文信息，AI能够更好地理解当前内容的语境，提高判断准确性，同时确保不会错误修改已经正确的内容。
